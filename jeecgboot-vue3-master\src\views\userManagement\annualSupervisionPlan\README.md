# 动态表单使用说明

## 概述
已成功将原始的动态表单改造为包含以下四个字段的监督项目动态表单：
- `userCode`: 用户代码
- `userName`: 用户名称  
- `authorizationTestingProject`: 授权测试项目
- `supervisionFrequency`: 监督频率

## 主要功能

### 1. 动态添加/删除项目
- 点击"添加监督项目"按钮可以添加新的监督项目
- 每个项目右上角有删除按钮，可以删除对应的项目
- 每个项目都有唯一的ID标识

### 2. 表单验证
- 所有字段都设置了必填验证
- 用户代码和用户名称为文本输入框
- 授权测试项目为文本输入框
- 监督频率为下拉选择框，包含以下选项：
  - 每月 (monthly)
  - 每季度 (quarterly)
  - 每半年 (semiannual)
  - 每年 (annual)
  - 不定期 (irregular)

### 3. 响应式布局
- 使用 Ant Design 的栅格系统
- 每行显示两个字段，在小屏幕上自动适配

## 文件结构

### 主要文件
- `modules/annualSupervisionPlanModal.vue`: 主要的模态框组件
- `test-dynamic-form.vue`: 独立的测试组件，用于验证动态表单功能

### 关键代码结构

#### 数据模型
```typescript
interface SupervisionItem {
  id: string;
  userCode: string;
  userName: string;
  authorizationTestingProject: string;
  supervisionFrequency: string;
}

const orderMainModel = reactive<any>({
  supervisionItems: [] as SupervisionItem[]
});
```

#### 主要方法
- `addSupervisionItem()`: 添加新的监督项目
- `removeSupervisionItem(item)`: 删除指定的监督项目
- `reset()`: 重置表单数据

## 使用方式

### 在现有组件中使用
```vue
<template>
  <!-- 动态表单项 -->
  <a-space 
    v-for="(item, index) in orderMainModel.supervisionItems" 
    :key="item.id" 
    style="display: flex; margin-bottom: 16px; width: 100%; border: 1px solid #d9d9d9; padding: 16px; border-radius: 6px;" 
    direction="vertical"
  >
    <!-- 表单内容 -->
  </a-space>
  
  <a-form-item>
    <a-button type="dashed" block @click="addSupervisionItem">
      <PlusOutlined />
      添加监督项目
    </a-button>
  </a-form-item>
</template>
```

### 测试组件
可以直接运行 `test-dynamic-form.vue` 来测试动态表单的功能。

## 注意事项

1. **表单验证**: 确保所有必填字段都有值才能提交
2. **数据结构**: 提交时数据结构为 `{ supervisionItems: SupervisionItem[] }`
3. **唯一标识**: 每个动态项目都有唯一的ID，用于删除操作
4. **类型安全**: 使用 TypeScript 接口确保类型安全

## 扩展建议

如果需要添加更多字段或修改现有字段：

1. 更新 `SupervisionItem` 接口定义
2. 在 `addSupervisionItem()` 方法中添加新字段的默认值
3. 在模板中添加对应的表单项
4. 更新验证规则（如果需要）

## 兼容性

- Vue 3 + TypeScript
- Ant Design Vue 3.x
- 支持响应式设计
- 兼容现有的 JeecgBoot 框架结构
