<template>
  <div style="padding: 20px;">
    <h2>动态表单测试</h2>
    <a-form 
      ref="formRef" 
      name="dynamic_supervision_form" 
      :model="dynamicValidateForm" 
      @finish="onFinish" 
    > 
      <!-- 动态表单项 -->
      <a-space 
        v-for="(item, index) in dynamicValidateForm.supervisionItems" 
        :key="item.id" 
        style="display: flex; margin-bottom: 16px; width: 100%; border: 1px solid #d9d9d9; padding: 16px; border-radius: 6px;" 
        direction="vertical"
      >
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
          <span style="font-weight: 500;">监督项目 {{ index + 1 }}</span>
          <MinusCircleOutlined 
            style="color: #ff4d4f; cursor: pointer;" 
            @click="removeSupervisionItem(item)" 
          />
        </div>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item 
              :name="['supervisionItems', index, 'userCode']" 
              label="用户代码" 
              :rules="{ required: true, message: '请输入用户代码' }"
            >
              <a-input 
                v-model:value="item.userCode" 
                placeholder="请输入用户代码"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item 
              :name="['supervisionItems', index, 'userName']" 
              label="用户名称" 
              :rules="{ required: true, message: '请输入用户名称' }"
            >
              <a-input 
                v-model:value="item.userName" 
                placeholder="请输入用户名称"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item 
              :name="['supervisionItems', index, 'authorizationTestingProject']" 
              label="授权测试项目" 
              :rules="{ required: true, message: '请输入授权测试项目' }"
            >
              <a-input 
                v-model:value="item.authorizationTestingProject" 
                placeholder="请输入授权测试项目"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item 
              :name="['supervisionItems', index, 'supervisionFrequency']" 
              label="监督频率" 
              :rules="{ required: true, message: '请选择监督频率' }"
            >
              <a-select 
                v-model:value="item.supervisionFrequency" 
                placeholder="请选择监督频率"
                :options="supervisionFrequencyOptions"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-space>
      
      <a-form-item>
        <a-button type="dashed" block @click="addSupervisionItem">
          <PlusOutlined />
          添加监督项目
        </a-button>
      </a-form-item>
      
      <a-form-item>
        <a-button type="primary" html-type="submit">
          提交
        </a-button>
        <a-button style="margin-left: 10px;" @click="resetForm">
          重置
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// 监督项目接口定义
interface SupervisionItem {
  id: string;
  userCode: string;
  userName: string;
  authorizationTestingProject: string;
  supervisionFrequency: string;
}

const formRef = ref();
let site = ref(0);

// 监督频率选项
const supervisionFrequencyOptions = [
  { label: '每月', value: 'monthly' },
  { label: '每季度', value: 'quarterly' },
  { label: '每半年', value: 'semiannual' },
  { label: '每年', value: 'annual' },
  { label: '不定期', value: 'irregular' }
];

const dynamicValidateForm = reactive({
  supervisionItems: [] as SupervisionItem[]
});

// 添加监督项目
function addSupervisionItem() {
  let curId = 'supervision_' + site.value;
  site.value++;
  dynamicValidateForm.supervisionItems.push({
    id: curId,
    userCode: '',
    userName: '',
    authorizationTestingProject: '',
    supervisionFrequency: ''
  });
}

// 删除监督项目
function removeSupervisionItem(item: SupervisionItem) {
  console.log("🚀 ~ removeSupervisionItem ~ item:", item);
  dynamicValidateForm.supervisionItems = dynamicValidateForm.supervisionItems.filter((supervisionItem: SupervisionItem) => supervisionItem.id !== item.id);
}

// 表单提交
function onFinish(values: any) {
  console.log('表单提交数据:', values);
  message.success('表单提交成功！');
}

// 重置表单
function resetForm() {
  formRef.value?.resetFields();
  dynamicValidateForm.supervisionItems = [];
  site.value = 0;
}

// 初始化时添加一个默认项目
addSupervisionItem();
</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>
