<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">
      <a-button type="primary" @click="chooseProjectFun">
        选择项目
      </a-button>
      <!-- 动态表单项 -->
      <a-space v-for="(item, index) in orderMainModel.supervisionItems" :key="item.id"
        style="display: flex; margin-bottom: 16px; width: 100%; border: 1px solid #d9d9d9; padding: 16px; border-radius: 6px;"
        direction="vertical">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
          <span style="font-weight: 500;">监督项目 {{ index + 1 }}</span>
          <MinusCircleOutlined style="color: #ff4d4f; cursor: pointer;" @click="removeSupervisionItem(item)" />
        </div>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'userCode']" label="用户代码"
              :rules="{ required: true, message: '请输入用户代码' }">
              <a-input v-model:value="item.userCode" placeholder="请输入用户代码" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'userName']" label="用户名称"
              :rules="{ required: true, message: '请输入用户名称' }">
              <a-input v-model:value="item.userName" placeholder="请输入用户名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'authorizationTestingProject']" label="授权测试项目"
              :rules="{ required: true, message: '请输入授权测试项目' }">
              <a-input v-model:value="item.authorizationTestingProject" placeholder="请输入授权测试项目" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'supervisionFrequency']" label="监督频率"
              :rules="{ required: true, message: '请填写监督频率' }">
              <a-input v-model:value="item.supervisionFrequency" placeholder="请填写监督频率" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'startTime']" label="开始时间"
              :rules="[
                { required: true, message: '请选择开始时间' },
                { pattern: /^\d{4}-\d{2}-\d{2}$/, message: '时间格式必须为YYYY-MM-DD' }
              ]">
              <a-date-picker
                v-model:value="item.startTime"
                placeholder="请选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="validateDateRange(item, index)" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'endTime']" label="结束时间"
              :rules="[
                { required: true, message: '请选择结束时间' },
                { pattern: /^\d{4}-\d{2}-\d{2}$/, message: '时间格式必须为YYYY-MM-DD' },
                { validator: (rule, value) => validateEndTime(value, item.startTime, index) }
              ]">
              <a-date-picker
                v-model:value="item.endTime"
                placeholder="请选择结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="validateDateRange(item, index)" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-space>

      <a-form-item>
        <a-button type="dashed" block @click="addSupervisionItem">
          <PlusOutlined />
          添加监督项目
        </a-button>
      </a-form-item>

    </a-form>
    <ChoosePersonModal @register="cpModal" @success="handleCpReturn"></ChoosePersonModal>
    <ChooseProjectModal @register="chooseProjectModal" @success="handleChooseProjectReturn"></ChooseProjectModal>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { saveOrUpdate } from '../annualSupervisionPlan.api';
import ChoosePersonModal from '/@/views/reagent/modules/ChoosePersonModal.vue'
import ChooseProjectModal from './ChooseProjectModal.vue'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
// 监督项目接口定义
interface SupervisionItem {
  id?: string;
  xid?: string;
  examId?: string;
  userCode: string;
  userName: string;
  authorizationTestingProject: string;
  supervisionFrequency: string;
  startTime: string;
  endTime: string;
}

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref();
const isUpdate = ref(true);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});

const orderMainModel = reactive<any>({
  supervisionItems: [] as SupervisionItem[]
});

let site = ref(0);

// 监督频率选项
const supervisionFrequencyOptions = [
  { label: '每月', value: 'monthly' },
  { label: '每季度', value: 'quarterly' },
  { label: '每半年', value: 'semiannual' },
  { label: '每年', value: 'annual' },
  { label: '不定期', value: 'irregular' }
];

const validatorRules = {

};
//表单配置
// const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
//     labelWidth: 150,
//     schemas: formSchema,
//     showActionButtonGroup: false,
// });
//表单赋值
const [cpModal, { openModal: choosePModal }] = useModal();
const [chooseProjectModal, { openModal: openChooseProjectModal }] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  formRef.value?.resetFields();
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {

    Object.assign(orderMainModel, data.record);
  } else {
  }
});
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit() {
  formRef.value
    ?.validate()
    .then(async () => {
      try {
        // let values = await validate();
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel.supervisionItems, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
  orderMainModel.supervisionItems = [];
  site.value = 0;
}

// 校验结束时间不能小于开始时间
function validateEndTime(endTime: string, startTime: string, index: number) {
  return new Promise((resolve, reject) => {
    if (!endTime || !startTime) {
      resolve(true);
      return;
    }

    const start = new Date(startTime);
    const end = new Date(endTime);

    if (end < start) {
      reject(new Error('结束时间不能小于开始时间'));
    } else {
      resolve(true);
    }
  });
}

// 日期范围校验
function validateDateRange(item: SupervisionItem, index: number) {
  if (item.startTime && item.endTime) {
    const start = new Date(item.startTime);
    const end = new Date(item.endTime);

    if (end < start) {
      // 触发表单校验
      formRef.value?.validateFields([`supervisionItems.${index}.endTime`]);
    }
  }
}


function handleCpReturn(source: any[]) {
  console.log("🚀 ~ file: PlanDevelopmentModal.vue:163 ~ handleCpReturn ~ source:", source)

  if (source.length != 0) {
    orderMainModel.userCode = '';
    orderMainModel.userName = '';

    for (let i = 0; i < source.length; i++) {
      orderMainModel.userCode += source[i].username;
      orderMainModel.userName += source[i].realname;

      if (i + 1 != source.length) {
        orderMainModel.userCode += ',';
        orderMainModel.userName += ',';
      }
    }
  }

}

// 添加监督项目
function addSupervisionItem() {
  let curXId = 'supervision_' + site.value;
  site.value++;
  orderMainModel.supervisionItems.push({
    xid: curXId,
    userCode: '',
    userName: '',
    authorizationTestingProject: '',
    supervisionFrequency: '次/项目年',
    startTime: '',
    endTime: ''
  });
}

// 删除监督项目
function removeSupervisionItem(item: SupervisionItem) {
  console.log("🚀 ~ removeSupervisionItem ~ item:", item);
  orderMainModel.supervisionItems = orderMainModel.supervisionItems.filter((supervisionItem: SupervisionItem) => supervisionItem.xid !== item.xid);
}

function chooseProjectFun() {
  openChooseProjectModal(true, {})
}
function handleChooseProjectReturn(value: any) {
  value[0].employeeExamDOS.forEach((item: any) => {
    orderMainModel.supervisionItems.push({
      xid: item.id,
      examId: item.id,
      userCode: item.commitPerson,
      userName: item.commitPerson,
      authorizationTestingProject: value[0].sopName,
      supervisionFrequency: '次/项目年',
      startTime: '',
      endTime: ''
    });
  });
}
</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>